﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS" />
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS&quot;&gt;&lt;security mode=&quot;Transport&quot; /&gt;&lt;/Data&gt;" bindingType="basicHttpBinding" name="BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://tst-ws.ankarasigorta.com.tr/YurtDisiSeyahatSaglikTibbiWS.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; contract=&quot;AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS&quot; name=&quot;BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://tst-ws.ankarasigorta.com.tr/YurtDisiSeyahatSaglikTibbiWS.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; contract=&quot;AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS&quot; name=&quot;BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; /&gt;" contractName="AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS" name="BasicHttpBinding_IYurtDisiSeyahatSaglikTibbiWS" />
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://tst-ws.ankarasigorta.com.tr/YurtDisiSeyahatSaglikTibbiWS.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; contract=&quot;AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS&quot; name=&quot;BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;https://tst-ws.ankarasigorta.com.tr/YurtDisiSeyahatSaglikTibbiWS.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; contract=&quot;AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS&quot; name=&quot;BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS&quot; /&gt;" contractName="AnkSigAbroadTravelHealthMedical.IYurtDisiSeyahatSaglikTibbiWS" name="BasicHttpsBinding_IYurtDisiSeyahatSaglikTibbiWS" />
  </endpoints>
</configurationSnapshot>