<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.Core</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1">
            <summary>
            Represents base type for binary format provider.
            </summary>
            <typeparam name="T">The type of the document.</typeparam>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1">
            <summary>
            Represents base binary format provider.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1">
            <summary>
            Represents interface for format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Import(System.IO.Stream)">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Export(`0,System.IO.Stream)">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>The supported extensions.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.CanImport">
            <summary>
            Gets a value indicating whether format provider can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.CanExport">
            <summary>
            Gets a value indicating whether format provider can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Import(System.IO.Stream)">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Export(`0,System.IO.Stream)">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ImportOverride(System.IO.Stream)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ExportOverride(`0,System.IO.Stream)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>
            The supported extensions.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.CanImport">
            <summary>
            Gets a value indicating whether format provider can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.CanExport">
            <summary>
            Gets a value indicating whether format provider can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1">
            <summary>
            Represents binary format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Import(System.Byte[])">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Import(System.Byte[])">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1">
            <summary>
            Represents interface for text base format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Import(System.String)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result string.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.OpenXml.Export.OpenXmlExportSettings">
            <summary>
            Represents OpenXml export settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.OpenXml.OpenXmlImportSettings">
            <summary>
            Represents OpenXml import settings.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName">
            <summary>
            Represents the possible groups of axes a group of series can be associated with.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName.Primary">
            <summary>
            Denotes the primary axes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName.Secondary">
            <summary>
            Denotes the secondary axes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.IStackCollectionElement">
            <summary>
            Represents StackCollection element.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.IStackCollectionElement.Name">
            <summary>
            Gets the name of the element.
            </summary>
            <value>The name.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1">
            <summary>
            Represents base class for text base format provider.
            </summary>
            <typeparam name="T">The type of the T.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Import(System.String)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The result document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result string.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Core.INamedObject">
            <summary>
            Represents named objects. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Core.INamedObject.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase">
            <summary>
            Provides the base class from which the classes that represent named objects are derived
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase"/> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase.Name">
            <summary>
            Gets the name value.
            </summary>
            <value>The name value.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter">
            <summary>
            A converter for the ThemableColor class. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert an object of the given type
            to the type of this converter, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="sourceType">A <see cref="T:System.Type" /> that represents the type
            you want to convert from.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert the object to the specified
            type, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="destinationType">A <see cref="T:System.Type" /> that represents the
            type you want to convert to.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified
            context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to
            use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object" /> to convert.</param>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed.
            </exception>
            <returns>
            An <see cref="T:System.Object" /> that represents the converted value.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Axis">
            <summary>
            Represents one of the axes of the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.Axis"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Min">
            <summary>
            Gets or sets the smallest value of the axis. If the value is null, the smallest value will be determined automatically.
            For data axes, this value is an OLE Automation date value. For categorical axis, this number is the number of the category in the
            succession of the category collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Max">
            <summary>
            Gets or sets the largest value of the axis. If the value is null, the largest value will be determined automatically.
            For data axes, this value is an OLE Automation date value. For categorical axis, this number is the number of the category in the
            succession of the category collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.IsVisible">
            <summary>
            Gets or sets the value indicating whether the axis is visible.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.NumberFormat">
            <summary>
            Gets or sets the format string that is applied to the values of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Outline">
            <summary>
            Gets the outline of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.MajorGridlines">
            <summary>
            Gets the major gridlines of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup">
            <summary>
            Represents a group of axes in the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.Axis,Telerik.Windows.Documents.Model.Drawing.Charts.Axis)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup"/> class.
            </summary>
            <param name="categoryAxis">The category axis.</param>
            <param name="valueAxis">The value axis.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.CategoryAxis">
            <summary>
            Gets or sets the axis agains which the categories of the series are plotted.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.ValueAxis">
            <summary>
            Gets or sets the axis agains which the values of the series are plotted.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType">
            <summary>
            Represents the possible types of axes of the chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Value">
            <summary>
            Denotes the value type of axis.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Category">
            <summary>
            Denotes the categorical type of axis.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Date">
            <summary>
            Denotes the date type of axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.CategoryAxis">
            <summary>
            Represents a categorical axis. The categorical axis is designed to contain discrete values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.CategoryAxis.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.CategoryAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DateAxis">
            <summary>
            Represents a date axis. The date axis is designed to contain date values. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DateAxis.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DateAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportAxes">
            <summary>
            Defines members for the classes representing series which can have axes. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportAxes.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ValueAxis">
            <summary>
            Represents a value axis. The value axis is designed to contain numeric values. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.ValueAxis.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ValueAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Legend">
            <summary>
            Represents teh legend of the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.Legend"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.Position">
            <summary>
            Gets or sets the value indicating where the legend will be positioned.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType">
            <summary>
            Represents the possible types of chart. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Bar">
            <summary>
            Denotes the bar type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Column">
            <summary>
            Denotes the column type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Pie">
            <summary>
            Denotes the pie type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Line">
            <summary>
            Denotes the line type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Area">
            <summary>
            Denotes the area type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Doughnut">
            <summary>
            Denotes the doughnut type of chart.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart">
            <summary>
            Represents a chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.SeriesGroups">
            <summary>
            Represents a collection of the groups in which the series of the chart are organized.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.PrimaryAxes">
            <summary>
            Represents the primary group of axes of the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.SecondaryAxes">
            <summary>
            Represents the secondary group of axes of the chart. It is used when there is more than one group of series (combo chart).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Title">
            <summary>
            Represents the title of the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Legend">
            <summary>
            Gets or sets the chart legend of the chart.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition">
            <summary>
            Represents the possible positions of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Right">
            <summary>
            Denotes the right-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Bottom">
            <summary>
            Denotes the bottom-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Left">
            <summary>
            Denotes the left-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Top">
            <summary>
            Denotes the top-side positioning of the legend.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeries">
            <summary>
            Represents a series of type area.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase">
            <summary>
            Represents a base class for the series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Values">
            <summary>
            Gets or sets the data for the values of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Categories">
            <summary>
            Gets or sets the data for the categories of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Title">
            <summary>
            Gets or sets the title of the series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeries.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup">
            <summary>
            Represents a group of series of type area.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1">
            <summary>
            Represents a base class for a group of series.
            </summary>
            <typeparam name="T">The type of the series that the group can hold.</typeparam>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup">
            <summary>
            Represents a base class for a group of series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.Series">
            <summary>
            Gets the collection of series of the group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1.Series">
            <summary>
            Gets the collection of series of the group. The series are represented by the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportGrouping">
            <summary>
            Defines members for the classes representing series which can be grouped. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportGrouping.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection">
            <summary>
            Represents the possible types of direction the bar series can have.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection.Bar">
            <summary>
            Represents the horizontal direction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection.Column">
            <summary>
            Represents the vertical direction.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeries">
            <summary>
            Represents a series of type bar.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeries.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup">
            <summary>
            Represents a group of series of type bar.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.BarDirection">
            <summary>
            Gets or sets the direction of the bars.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType">
            <summary>
            Represents the possible types of chart data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.Formula">
            <summary>
            Represents the formula chart data type. This type of chart data holds a formula which refers to the actual data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.NumericLiteral">
            <summary>
            Represents the numeric literals type. This type of chart data holds a series of numbers as data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.StringLiteral">
            <summary>
            Represents the string literals type. This type of chart data holds a series of strings as data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup">
            <summary>
            Represents a group of series of type doughnut.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup">
            <summary>
            Represents a group of series of type pie.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup.HoleSizePercent">
            <summary>
            Gets or sets the relative size of the hole of the doughnut as percent of the hole. The value is limited between 0 and 90.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData">
            <summary>
            Represents formula chart data. This type of chart data holds a formula which refers to the actual data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData">
            <summary>
            Defines the members for the classes representing chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData"/> class.
            </summary>
            <param name="formula">The formula which refers to the chart data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.Formula">
            <summary>
            Gets the formula which refers to the chart data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping">
            <summary>
            Denotes the possible types of grouping for a group of series.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.Standard">
            <summary>
            Denotes the standard type of grouping. For bar series, the series will be clustered.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.Stacked">
            <summary>
            Denotes the stacked type of grouping.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.PercentStacked">
            <summary>
            Denotes the percent stacked type of grouping.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries">
            <summary>
            Represents a series of type line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup">
            <summary>
            Represents a group of series of type line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData">
            <summary>
            Represents the numeric literals chart data. This type of chart data holds a series of numbers as data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.#ctor(System.Collections.Generic.IEnumerable{System.Double})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.NumericLiterals">
            <summary>
            Gets the numeric chart data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeries">
            <summary>
            Represents a series of type pie.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeries.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection">
            <summary>
            Represents a base class for a collection of series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.GetEnumerator">
            <summary>
            Enumerates the series in the collection.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add">
            <summary>
            Creates a new series, adds it to the collection and returns it.
            </summary>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series, adds it to the collection and returns it.
            </summary>
            <param name="categoriesData">The data for the categories of the series.</param>
            <param name="valuesData">The data for the values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase)">
            <summary>
            Adds a new series to the collection.
            </summary>
            <param name="series">The series to be added.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Remove(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase)">
            <summary>
            Removes the specified series from the collection.
            </summary>
            <param name="series">The series to be removed.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1">
            <summary>
            Represents a collection of series.
            </summary>
            <typeparam name="T">The type of series the collection holds.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.GetEnumerator">
            <summary>
            Enumerates the series in the collection as objects of a concrete series class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Add">
            <summary>
            Creates a new series, adds it to the collection and returns it.
            </summary>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Add(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series using the specified data, adds it to the collection and returns it.
            </summary>
            <param name="categoriesData">The data for the categories of the series.</param>
            <param name="valuesData">The data for the values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Add(`0)">
            <summary>
            Adds a new series to the collection.
            </summary>
            <param name="series">The series to be added.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Remove(`0)">
            <summary>
            Removes the specified series from the collection.
            </summary>
            <param name="series">The series to be removed.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection">
            <summary>
            Represents a group of series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup)">
            <summary>
            Adds a new group of series to the collection.
            </summary>
            <param name="seriesGroup">The new group of series.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.Remove(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup)">
            <summary>
            Removes the specified group of series from the collection,
            </summary>
            <param name="seriesGroup"></param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.GetEnumerator">
            <summary>
            Enumerates the elements of the collection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType">
            <summary>
            Represents the possible types of series.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Bar">
            <summary>
            Represents the bar series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Line">
            <summary>
            Represents the line series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Pie">
            <summary>
            Represents the pie series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Area">
            <summary>
            Represents the area series type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData">
            <summary>
            Represents the string literals chart data. This type of chart data holds a series of strings as data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.StringLiterals">
            <summary>
            Gets the string chart data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle">
            <summary>
            Represent the formula chart title. This type of title holds a formula which refers to the actual title.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Title">
            <summary>
            Represents a base class for the classes representing a title in the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Title.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Title.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.Formula">
            <summary>
            Gets the formula referring to the actual title.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle">
            <summary>
            Represents the text chart title. This type of title holds a text literal to be used as title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.Text">
            <summary>
            Gets the text of the title.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType">
            <summary>
            Represents the possible types of chart title.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType.Formula">
            <summary>
            Denotes the formula chart title type. This type of title holds a formula which refers to the actual title.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType.Text">
            <summary>
            Denotes the text chart title type. This type of title holds a text literal to be used as title.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine">
            <summary>
            Represents a line in the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine.Outline">
            <summary>
            Gets the outline properties of the line.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.NoFill">
            <summary>
            Represents the lack of fill of an object.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.Fill">
            <summary>
            Represents the fill of an object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.Fill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.NoFill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase">
            <summary>
            Represents shape base element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.#ctor(Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase"/> class by copying an Image instance.
            </summary>
            <param name="other">The other image.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.InitializeSize">
            <summary>
            Initializes the size.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SetWidth(System.Boolean,System.Double)">
            <summary>
            Sets the width of the shape.
            </summary>
            <param name="respectLockAspectRatio">A value indicating whether the aspect ratio lock should be respected.</param>
            <param name="width">The new width.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SetHeight(System.Boolean,System.Double)">
            <summary>
            Sets the height of the shape.
            </summary>
            <param name="respectLockAspectRatio">A value indicating whether the aspect ratio lock should be respected.</param>
            <param name="height">The new height.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Width">
            <summary>
            Gets or sets the width.
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Height">
            <summary>
            Gets or sets the height.
            </summary>
            <value>The height.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Size">
            <summary>
            Gets or sets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SizeInternal">
            <summary>
            Gets the size silently so the size's auto-initializing cannot be invoked.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.IsVerticallyFlipped">
            <summary>
            Gets or sets the value indicating if the shape is vertically flipped.
            </summary>
            <value>The value indicating if the shape is vertically flipped.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.IsHorizontallyFlipped">
            <summary>
            Gets or sets the value indicating if the shape is horizontally flipped.
            </summary>
            <value>The value indicating if the shape is horizontally flipped.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.RotationAngle">
            <summary>
            Gets or sets the rotation angle.
            </summary>
            <value>The rotation angle.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.LockAspectRatio">
            <summary>
            Gets or sets the value indicating whether the aspect ratio between the width and height should remain constant.
            </summary>
            <value>The value indicating whether the aspect ratio between the width and height should remain constant.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Fill">
            <summary>
            Gets or sets the fill of the shape.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Outline">
            <summary>
            Gets the outline of the shape.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image">
            <summary>
            Represents image element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.#ctor(Telerik.Windows.Documents.Model.Drawing.Shapes.Image)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image"/> class.
            </summary>
            <param name="other">The other image.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.InitializeSize">
            <summary>
            Initializes the size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.ImageSource">
            <summary>
            Gets or sets the image source.
            </summary>
            <value>The image source.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.PreferRelativeToOriginalResize">
            <summary>
            Gets or sets the value indicating whether the scale resizing should be relative to the original or the current size of the image.
            </summary>
            <value>The value indicating whether the scale resizing should be relative to the original or the current size of the image.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.Outline">
            <summary>
            Represents the outline of an object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Fill">
            <summary>
            Gets or sets the fill of the outline.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Width">
            <summary>
            Gets ot sets the width of the line in points.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill">
            <summary>
            Represents a solid fill of an object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.#ctor(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.#ctor(System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.Color">
            <summary>
            Represents the color of the fill.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType">
            <summary>
            Defines the types of color shade.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade1">
            <summary>
            Represents shade 1 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade2">
            <summary>
            Represents shade 2 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade3">
            <summary>
            Represents shade 3 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade4">
            <summary>
            Represents shade 4 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade5">
            <summary>
            Represents shade 5 shade type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.#ctor(System.String,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="colorScheme">The color scheme.</param>
            <param name="fontScheme">The font scheme.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.Clone">
            <summary>
            Creates deep copy of this document theme.
            </summary>
            <returns>The cloned document theme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.ColorScheme">
            <summary>
            Gets the color scheme.
            </summary>
            <value>The color scheme.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.FontScheme">
            <summary>
            Gets the font scheme.
            </summary>
            <value>The font scheme.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType">
            <summary>
            Describes the types of font languages.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.Latin">
            <summary>
            Represents latin font language type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.EastAsian">
            <summary>
            Represents east asian font language type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.ComplexScript">
            <summary>
            Represents complex script font language type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1">
            <summary>
            Defines the members of an object which is part of a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.LocalValue">
            <summary>
            Gets the local value.
            </summary>
            <value>The local value.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes">
            <summary>
            Describes the color and font schemes for the predefined document themes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.DefaultTheme">
            <summary>
            The default document theme.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.ColorSchemes">
            <summary>
            Predefined color schemes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.FontSchemes">
            <summary>
            Predefined font schemes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor">
            <summary>
            Represents a color which can be used in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor"/> class.
            </summary>
            <param name="color">The color.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(System.Windows.Media.Color,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor"/> class.
            </summary>
            <param name="color">The color.</param>
            <param name="isAutomatic">The is automatic.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,System.Nullable{Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor"/> class.
            </summary>
            <param name="themeColorType">The theme color type.</param>
            <param name="colorShadeType">The color shade type.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor"/> class.
            </summary>
            <param name="themeColorType">Theme color type.</param>
            <param name="tintAndShade">The tint and shade.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates new themable color from Argb.
            </summary>
            <param name="alfa">The alfa.</param>
            <param name="red">The red.</param>
            <param name="green">The green.</param>
            <param name="blue">The blue.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Equality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor,Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Compares two themable colors.
            </summary>
            <param name="first">The first themable color.</param>
            <param name="second">The second themable color.</param>
            <returns>If the two themable colors are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Inequality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor,Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Compares two themable colors.
            </summary>
            <param name="first">The first themable color.</param>
            <param name="second">The second themable color.</param>
            <returns>If the two themable colors are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Explicit(System.Windows.Media.Color)~Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor">
            <summary>
            Explicitly cast color to themable color.
            </summary>
            <param name="value">The color.</param>
            <returns>Themable color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="colorScheme">The color scheme.</param>
            <returns>The actual color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            True if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.LocalValue">
            <summary>
            Gets the local value of the color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.IsAutomatic">
            <summary>
            Gets the value indicating if the color is automatic. Automatic colors may be modified by a consumer as appropriate.
            </summary>
            <value>Value indicating if the color is automatic.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ThemeColorType">
            <summary>
            Gets the theme color type.
            </summary>
            <value>The theme color type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ColorShadeType">
            <summary>
            Gets the color shade type.
            </summary>
            <value>The color shade type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.TintAndShade">
            <summary>
            Gets the tint and shade.
            </summary>
            <value>The tint and shade.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily">
            <summary>
            Represents a font family which can be used in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(System.Windows.Media.FontFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily"/> class.
            </summary>
            <param name="fontFamily">The font family.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily"/> class.
            </summary>
            <param name="familyName">Name of the family.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily"/> class.
            </summary>
            <param name="themeFontType">Type of the theme font.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Equality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily,Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily)">
            <summary>
            Compares two themable font families.
            </summary>
            <param name="first">The first themable font family.</param>
            <param name="second">The second themable font family.</param>
            <returns>If the two themable font families are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Inequality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily,Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily)">
            <summary>
            Compares two themable font families.
            </summary>
            <param name="first">The first themable font family.</param>
            <param name="second">The second themable font family.</param>
            <returns>If the two themable font families are not equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Explicit(System.Windows.Media.FontFamily)~Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily">
            <summary>
            Casts explicitly FontFamily object to themable font family.
            </summary>
            <param name="value">The font family.</param>
            <returns>Themable font family.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents the current
            <see cref="T:System.Object" />.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            True if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.LocalValue">
            <summary>
            Gets the local value.
            </summary>
            <value>The local value.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.ThemeFontType">
            <summary>
            Gets the theme font type.
            </summary>
            <value>The theme font type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor">
            <summary>
            Represents a color in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.#ctor(System.Windows.Media.Color,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor"/> class.
            </summary>
            <param name="color">The color.</param>
            <param name="themeColorType">Type of the theme color.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Clone">
            <summary>
            Creates deep copy of this theme color.
            </summary>
            <returns>The cloned theme color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.ThemeColorType">
            <summary>
            Gets the type of the theme color.
            </summary>
            <value>The type of the theme color.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Color">
            <summary>
            Gets the color.
            </summary>
            <value>The color.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme">
            <summary>
            Represents the color scheme of a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.#ctor(System.String,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="background1">The first background.</param>
            <param name="text1">The first text color.</param>
            <param name="background2">The second background.</param>
            <param name="text2">The second text color.</param>
            <param name="accent1">The first accent.</param>
            <param name="accent2">The second accent.</param>
            <param name="accent3">The third accent.</param>
            <param name="accent4">The fourth accent.</param>
            <param name="accent5">The fifth accent.</param>
            <param name="accent6">The sixth accent.</param>
            <param name="hyperlink">The hyperlink color.</param>
            <param name="followedHyperlink">The followed hyperlink color.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetTintAndShade(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType)">
            <summary>
            Gets the tint and shade.
            </summary>
            <param name="themeColorType">Type of the theme color.</param>
            <param name="colorShadeType">Type of the color shade.</param>
            <returns>The tint and shade value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Clone">
            <summary>
            Creates deep copy of this theme color scheme.
            </summary>
            <returns>The cloned theme color scheme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Item(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor"/> with the specified color type.
            </summary>
            <value>The theme color.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType">
            <summary>
            Defines the types of theme colors.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Background1">
            <summary>
            Represents Background1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Text1">
            <summary>
            Represents Text1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Background2">
            <summary>
            Represents Background2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Text2">
            <summary>
            Represents Text2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent1">
            <summary>
            Represents Accent1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent2">
            <summary>
            Represents Accent2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent3">
            <summary>
            Represents Accent3 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent4">
            <summary>
            Represents Accent4 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent5">
            <summary>
            Represents Accent5 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent6">
            <summary>
            Represents Accent6 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Hyperlink">
            <summary>
            Represents Hyperlink theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.FollowedHyperlink">
            <summary>
            Represents FollowedHyperlink theme color type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont">
            <summary>
            Represents the font in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.#ctor(System.Windows.Media.FontFamily,Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont"/> class.
            </summary>
            <param name="fontFamily">The font family.</param>
            <param name="fontLanguageType">The type of font language.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.#ctor(System.String,Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont"/> class.
            </summary>
            <param name="fontName">Name of the font.</param>
            <param name="fontLanguageType">Type of the font language.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.GetHashCode">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.FontFamily">
            <summary>
            Gets the font family.
            </summary>
            <value>The font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.FontLanguageType">
            <summary>
            Gets the type of the font language.
            </summary>
            <value>The type of font language.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts">
            <summary>
            A collection of fonts for a theme, each corresponding to a language type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts"/> class.
            </summary>
            <param name="latinFontName">Name of the latin font.</param>
            <param name="eastAsianFontName">Name of the east asian font.</param>
            <param name="complexScriptFontName">Name of the complex script font.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Clone">
            <summary>
            Creates deep copy of this theme fonts.
            </summary>
            <returns>The cloned theme fonts.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Item(Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont"/> with the specified font language type.
            </summary>
            <value>The theme font.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme">
            <summary>
            Represents the font scheme of a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme"/> class.
            </summary>
            <param name="name">The name.</param>
            <param name="latinMajorFontName">Name of the latin headings font.</param>
            <param name="latinMinorFontName">Name of the latin body font.</param>
            <param name="eastAsianMajorFontName">Name of the east asian headings font.</param>
            <param name="eastAsianMinorFontName">Name of the east asian body font.</param>
            <param name="complexScriptMajorFontName">Name of the complex script headings font.</param>
            <param name="complexScriptMinorFontName">Name of the complex script body font.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Clone">
            <summary>
            Creates deep copy of this theme font scheme.
            </summary>
            <returns>The cloned theme font scheme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Item(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts"/> with the specified font type.
            </summary>
            <value>The theme fonts.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType">
            <summary>
            Describes the types of theme fonts.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType.Minor">
            <summary>
            The font of the body of the document.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType.Major">
            <summary>
            The font of the headings of the document.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.GetDigestSize">
             return the size, in bytes, of the digest produced by this message digest.
            
             @return the size, in bytes, of the digest produced by this message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.GetByteLength">
             return the size, in bytes, of the internal buffer used by this digest.
            
             @return the size, in bytes, of the internal buffer used by this digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.Update(System.Byte)">
             update the message digest with a single byte.
            
             @param inByte the input byte to be entered.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.BlockUpdate(System.Byte[],System.Int32,System.Int32)">
             update the message digest with a block of bytes.
            
             @param input the byte array containing the data.
             @param inOff the offset into the byte array where the data starts.
             @param len the length of the data.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.DoFinal(System.Byte[],System.Int32)">
             Close the digest, producing the final digest value. The doFinal
             call leaves the digest reset.
            
             @param output the array the digest is to be copied into.
             @param outOff the offset into the out array the digest is to start at.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.Reset">
            reset the digest back to it's initial state.
        </member>
        <member name="P:Telerik.Windows.Documents.Common.Model.Protection.IDigest.AlgorithmName">
             return the algorithm name
            
             @return the algorithm name
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.Encode(System.Byte[],System.Int32,System.Int32,System.IO.Stream)">
             encode the input data producing a Hex output stream.
            
             @return the number of bytes produced.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.Decode(System.Byte[],System.Int32,System.Int32,System.IO.Stream)">
             decode the Hex encoded byte data writing it to the given output stream,
             whitespace characters will be ignored.
            
             @return the number of bytes produced.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.DecodeString(System.String,System.IO.Stream)">
             decode the Hex encoded string data writing it to the given output stream,
             whitespace characters will be ignored.
            
             @return the number of bytes produced.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.#ctor">
            Constructor for variable length word
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.#ctor(Telerik.Windows.Documents.Common.Model.Protection.LongDigest)">
            Copy constructor.  We are using copy constructors in place
            of the object.Clone() interface as this interface is not
            supported by J2ME.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.AdjustByteCounts">
            adjust the byte counts so that byteCount2 represents the
            upper long (less 3 bits) word of the byte count.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.GenerateSaltBase64">
            <summary>
            Generates base64 salt.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.GenerateHashBase64(System.String,System.String,System.String,System.Int32)">
            <summary>
            Generates base64 hash.
            </summary>
            <param name="salt">The salt.</param>
            <param name="password">The password.</param>
            <param name="algorithmName">Name of the algorithm.</param>
            <param name="spinCount">The spin count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.IsPasswordCorrect(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            Determines whether [is password correct] [the specified password].
            </summary>
            <param name="password">The password.</param>
            <param name="hash">The hash.</param>
            <param name="salt">The salt.</param>
            <param name="algorithmName">Name of the algorithm.</param>
            <param name="spinCount">The spin count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.#ctor">
            Standard constructor
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.#ctor(Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.Reset">
            reset the chaining variables to the IV values.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA384.#ctor(Telerik.Windows.Documents.Common.Model.Protection.SHA384)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA384.Reset">
            reset the chaining variables
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA512.#ctor(Telerik.Windows.Documents.Common.Model.Protection.SHA512)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA512.Reset">
            reset the chaining variables
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.Whirlpool.#ctor(Telerik.Windows.Documents.Common.Model.Protection.Whirlpool)">
            Copy constructor. This will copy the state of the provided message
            digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.Whirlpool.Reset">
            Reset the chaining variables
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2">
            <summary>
            Provides based functionality for collections of document elements.
            </summary>
            <typeparam name="T">The type of the document elements which should be added to the collection.</typeparam>
            <typeparam name="TOwner">The type of the owner of the collection.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
            be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.IndexOf(`0)">
            <summary>
            Determines the index of a specific element in the collection.
            </summary>
            <param name="item">The element to locate in the collection.</param>
            <returns>
            The index of <paramref name="item" /> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Insert(System.Int32,`0)">
            <summary>
            Inserts an element to the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
            <param name="item">The element to insert into the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Inserts an elements to the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="items" /> should be inserted.</param>
            <param name="items">The elements to be inserted into the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.RemoveRange(System.Int32,System.Int32)">
            <summary>
            Removes the range.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <param name="count">The number of elements to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.OnAfterRemove(`0)">
            <summary>
            Called when the element is removed.
            </summary>
            <param name="item">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Contains(`0)">
            <summary>
            Determines whether the collection contains a specific element.
            </summary>
            <param name="item">The element to locate in the collection.</param>
            <returns>
            <c>true</c> if <paramref name="item" /> is found in the collection; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.CopyTo(`0[],System.Int32)">
            <summary>
            Copies the elements of the collection to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Remove(`0)">
            <summary>
            Removes the first occurrence of a specific object from the collection.
            </summary>
            <param name="item">The object to remove from the collection.</param>
            <returns>
            <c>true</c> if <paramref name="item" /> was successfully removed from the collection; otherwise, <c>false</c>. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be
            used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Add(`0)">
            <summary>
            Adds an item to the collection.
            </summary>
            <param name="item">The element to add to the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.VerifyDocumentElementOnInsert(`0)">
            <summary>
            Verifies the validity of the document element before it is inserted in the collection. 
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.SetParent(`0,`1)">
            <summary>
            Sets the parent of the document element.
            </summary>
            <param name="item">The document element.</param>
            <param name="parent">The parent which should be set to the document element.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
            <returns>The number of elements contained in the collection.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
            <returns><c>true</c> if the collection is read-only; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Owner">
            <summary>
            Gets the element holding the collection.
            </summary>
            <value>The owner element.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.StackCollection`1">
            <summary>
            Represents Stack collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.#ctor">
            <summary>
            Initializes a new instance of the StackCollection class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.GetElementByName(System.String)">
            <summary>
            Gets the name of the element by.
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Add(`0)">
            <summary>
            Adds the specified item.
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddBefore(System.String,`0)">
            <summary>
            Adds the before.
            </summary>
            <param name="presentedElementName">Name of the presented element.</param>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddAfter(System.String,`0)">
            <summary>
            Adds the after.
            </summary>
            <param name="presentedElementName">Name of the presented element.</param>
            <param name="element">The element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddFirst(`0)">
            <summary>
            Adds the first.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddLast(`0)">
            <summary>
            Adds the last.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Contains(`0)">
            <summary>
            Determines whether [contains] [the specified item].
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified element name].
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Remove(`0)">
            <summary>
            Removes the specified item.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Remove(System.String)">
            <summary>
            Removes the specified element name.
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.CopyTo(`0[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only. </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
            be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be
            used to iterate through the collection.
            </returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.StackCollection`1.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
            <returns>The number of elements contained in the collection.
            </returns>
            <value></value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.StackCollection`1.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only; otherwise, false.</returns>
            <value></value>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadLittleEndianInt32(System.Byte[],System.Int32)">
            <summary>
            Reads the little endian 4 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadInt16(System.Byte[],System.Int32)">
            <summary>
            Reads 2 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadInt32(System.Byte[],System.Int32)">
            <summary>
            Reads 4 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.ScanEncoder.PrepareQuantizationTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare base quantization tables.
            </summary>
            <param name="encoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.ScanEncoder.PrepareHuffmanTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare Huffman tables.
            </summary>
            <param name="jpegEncoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.BaselineDCTEncoder.PrepareHuffmanTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare Huffman tables.
            </summary>
            <param name="jpegEncoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.BaselineDCTEncoder.PrepareQuantizationTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare base quantization tables.
            </summary>
            <param name="encoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.#ctor(Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegImage,Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters)">
            <summary>
            Initializes a new instance of the JpegEncoder class.
            </summary>
            <param name="jpegImage">JPEG image to be encoded.</param>
            <param name="encoderParameters">JPEG encoder parameters.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Height">
            <summary>
            Gets number of lines (height). 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Parameters">
            <summary>
            Gets encoder parameters.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Width">
            <summary>
            Gets number of the samples per line (width).
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters">
            <summary>
            Represents parameters of the JPEG encoder.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.#ctor">
            <summary>
            Initializes a new instance of the JpegEncoderParameters class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.ChrominanceTable">
            <summary>
            Gets or sets a 64 byte array which corresponds to a JPEG Chrominance Quantization table.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.EncodingType">
            <summary>
            Gets or sets type of the JPEG encoder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.LuminanceTable">
            <summary>
            Gets or sets a 64 byte array which corresponds to a JPEG Luminance Quantization table.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.QuantizingQuality">
            <summary>
            Gets or sets quantizing quality.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.SamplePrecision">
            <summary>
            Gets or sets the precision in bits for the samples of the components in the frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see P parameter in the Table B.2).</remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType">
            <summary>
            Type of the JPEG encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.BaselineDct">
            <summary>
            Baseline DCT encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.ProgressiveDct">
            <summary>
            Progressive DCT encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.NotSupported">
            <summary>
            Indicates that JPEG uses not-supported encoding type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.#ctor">
            <summary>
            Initializes a new instance of the WriterBase class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Write(System.Byte)">
            <summary>
            Writes single byte.
            </summary>
            <param name="byteToWrite"></param>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Write(System.Byte[],System.Int32)">
            <summary>
            Writes a block of bytes.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="count">Bytes count.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.WriteBE(System.Byte[],System.Int32)">
            <summary>
            Writes buffer in reverce direction.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="count">Bytes count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position.
            </summary>
            <param name="offset"></param>
            <param name="origin"></param>
        </member>
        <member name="P:Telerik.Windows.Documents.Utilities.WriterBase.Data">
            <summary>
            Gets writer data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.#ctor">
            <summary>
            Initializes a new instance of the JpegWriter class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write4(System.Byte)">
            <summary>
            Write 4 bits from the given byte value.
            </summary>
            <param name="value">Byte to get bits from.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write8(System.Byte)">
            <summary>
            Write byte,
            </summary>
            <param name="value">Byte to write.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write16(System.UInt16)">
            <summary>
            Write usignded short value.
            </summary>
            <param name="value">Value to write.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteJpegTables``1(System.Collections.Generic.IEnumerable{``0},System.UInt16)">
            <summary>
            Write JPEG information structures.
            </summary>
            <typeparam name="T">Table type.</typeparam>
            <param name="tables">Tables to write.</param>
            <param name="extraBytesCount">Numbe of the extra bytes whould be added to the length of the table list.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteBits(System.Int32,System.Int32)">
            <summary>
            Writes a bits.
            </summary>
            <param name="n">Number of bits.</param>
            <param name="bits">Value to get bits from.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteJpegMarker(Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker)">
            <summary>
            Writes a JPEG marker.
            </summary>
            <param name="marker">Mrker to write.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Restart">
            <summary>
            Restart buffer writer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.MarkerType">
            <summary>
            Gets marker type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.Code">
            <summary>
            Gets marker code.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker">
            <summary>
            Represents JFIF segment (APP0 marker).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.JFIF_Identifier">
            <summary>
            JFIF segment format.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.JFXX_Identifier">
            <summary>
            JFIF extension (JFXX) segment format. Currently is not supported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityUnits">
            <summary>
            Gets or sets units for pixel density fields.
            </summary>
            <remarks>
            <list type="bullet">
            <item>0 - No units, aspect ratio only specified.</item>
            <item>1 - Pixels per inch.</item>
            <item>2 - Pixels per centimetre.</item>
            </list>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityX">
            <summary>
            Gets or sets horizontal pixel density.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityY">
            <summary>
            Gets or sets vertical pixel density.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.Identifier">
            <summary>
            Gets or sets identifier.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailData">
            <summary>
            Gets or sets embedded JFIF thumbnail data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailHeight">
            <summary>
            Gets or sets vertical size of embedded JFIF thumbnail in pixels.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailWidth">
            <summary>
            Gets or sets horizontal size of embedded JFIF thumbnail in pixels.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FloatBlock">
            <summary>
            Represents block of float values.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.Clear">
            <summary>
            Clear buffer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.WriteBits(System.Byte,System.Int32)">
            <summary>
            Write bits into the internal buffer.
            </summary>
            <param name="value">Value to get bits from.</param>
            <param name="n">Number of bits.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.BitsLeft">
            <summary>
            Gets number of the bits left in the buffer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.IsEmpty">
            <summary>
            Gets value which indicates that buffer is empty (no bits have been written).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.IsFull">
            <summary>
            Gets value which indicates whether all bits in the buffer have been written.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.Data">
            <summary>
            Gets writer data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException">
            <summary>
            Represents a not supported feature exception.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException">
            <summary>
            Represents not supported scan decoder exception.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException">
            <summary>
            Represents not supported scan decoder exception.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException"/> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException"/> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException"/> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase">
            <summary>
            Represents pointer handlers controller base class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.ImageSource">
            <summary>
            Encapsulates data needed for creation of an image
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.ImageSource.#ctor(System.IO.Stream,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Media.ImageSource"/> class.
            </summary>
            <param name="stream">The stream which represents the image.</param>
            <param name="extension">The format of the image.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.ImageSource.#ctor(System.Byte[],System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Media.ImageSource"/> class.
            </summary>
            <param name="data">Byte array containing representation of the image source.</param> 
            <param name="extension">The image file extension.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Media.ImageSource.Extension">
            <summary>
            Gets the image file extension.
            </summary>
            <value>The image file extension.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Media.ImageSource.Data">
            <summary>
            Gets the byte array representation of the image source.
            </summary>
            <value>The byte array representation of the image source.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PageOrientation">
            <summary>
            Specifies page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Portrait">
            <summary> 
            Portrait page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Landscape">
            <summary> 
            Landscape page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate180">
            <summary> 
            Page is rotated 180 degrees.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate270">
            <summary> 
            Page is rotated 270 degrees
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate0">
            <summary> 
            Portrait page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate90">
            <summary> 
            Landscape page orientation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PaperTypeConverter">
            <summary>
            Provides methods for converting from standard PaperTypes to Size
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.PaperTypeConverter.ToSize(Telerik.Windows.Documents.Model.PaperTypes)">
            <summary>
            Converts the specified PaperType enumeration to a pair of pixel values in Size.
            </summary>
            <param name="type">PaperType</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PaperTypes">
            <summary>
            Provides enumeration for the most commonly used paper sizes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A0">
            <summary>
            Identifies a paper sheet size of 33.1 inches x 46.8 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A1">
            <summary>
            Identifies a paper sheet size of 23.4 inches x 33.1 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A2">
            <summary>
            Identifies a paper sheet size of 16.5 inches x 23.4 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A3">
            <summary>
            Identifies a paper sheet size of 11.7 inches x 16.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A4">
            <summary>
            Identifies a paper sheet size of 8.3 inches x 11.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A5">
            <summary>
            Identifies a paper sheet size of 5.8 inches x 8.3 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA0">
            <summary>
            Identifies a paper sheet size of 33.9 inches x 48 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA1">
            <summary>
            Identifies a paper sheet size of 24 inches x 33.9 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA2">
            <summary>
            Identifies a paper sheet size of 16.9 inches x 24 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA3">
            <summary>
            Identifies a paper sheet size of 12 inches x 16.9 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA4">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 12 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA5">
            <summary>
            Identifies a paper sheet size of 4.8 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B0">
            <summary>
            Identifies a paper sheet size of 39.4 inches x 55.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B1">
            <summary>
            Identifies a paper sheet size of 27.8 inches x 39.4 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B2">
            <summary>
            Identifies a paper sheet size of 59.1 inches x 19.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B3">
            <summary>
            Identifies a paper sheet size of 13.9 inches x 19.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B4">
            <summary>
            Identifies a paper sheet size of 10.1 inches x 14.3 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B5">
            <summary>
            Identifies a paper sheet size of 7.2 inches x 10.1 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Quarto">
            <summary>
            Identifies a paper sheet size of 8 inches x 10 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Foolscap">
            <summary>
            Identifies a paper sheet size of 8 inches x 13 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Executive">
            <summary>
            Identifies a paper sheet size of 7.5 inches x 10 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.GovernmentLetter">
            <summary>
            Identifies a paper sheet size of 10.5 inches x 8 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Letter">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 11 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Legal">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 14 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Ledger">
            <summary>
            Identifies a paper sheet size of 17 inches x 11 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Tabloid">
            <summary>
            Identifies a paper sheet size of 11 inches x 17 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Post">
            <summary>
            Identifies a paper sheet size of 15.6 inches x 19.2 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Crown">
            <summary>
            Identifies a paper sheet size of 20 inches x 15 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.LargePost">
            <summary>
            Identifies a paper sheet size of 16.5 inches x 21 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Demy">
            <summary>
            Identifies a paper sheet size of 17.5 inches x 22 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Medium">
            <summary>
            Identifies a paper sheet size of 18 inches x 23 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Royal">
            <summary>
            Identifies a paper sheet size of 20 inches x 25 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Elephant">
            <summary>
            Identifies a paper sheet size of 21.7 inches x 28 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.DoubleDemy">
            <summary>
            Identifies a paper sheet size of 23.5 inches x 35 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.QuadDemy">
            <summary>
            Identifies a paper sheet size of 35 inches x 45 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.STMT">
            <summary>
            Identifies a paper sheet size of 5.5 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Folio">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 13 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Statement">
            <summary>
            Identifies a paper sheet size of 5.5 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Size10x14">
            <summary>
            Identifies a paper sheet size of 10 inches x 14 inches.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.Unit">
            <summary>
            Contains methods for converting DPI(Device Independent Pixels) to other unit types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPoint(System.Double)">
            <summary>
            Converts dips to points.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPointI(System.Double)">
            <summary>
            Converts dips to points.
            </summary>
            <param name="pixels">Pixels.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPica(System.Double)">
            <summary>
            Converts dips to picas.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Picas.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToCm(System.Double)">
            <summary>
            Converts dips to centimeters.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Centimeters.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToMm(System.Double)">
            <summary>
            Converts dips to millimeters.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Millimeters.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToInch(System.Double)">
            <summary>
            Converts dips to inches.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Inches.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwip(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToEmu(System.Double)">
            <summary>
            Converts dips to EMUs.
            </summary>
            <param name="value">Pixels.</param>
            <returns>EMUs.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToEmuI(System.Double)">
            <summary>
            Converts dips to EMUs.
            </summary>
            <param name="value">Pixels.</param>
            <returns>EMUs.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwipI(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwipF(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDipF(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Dips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDipI(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Dips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToUnit(System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts DIPs to units.
            </summary>
            <param name="value">Pixels.</param>
            <param name="type">UnitType.</param>
            <returns>Units.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PointToDip(System.Double)">
            <summary>
            Converts to points dips.
            </summary>
            <param name="value">Points.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PicaToDip(System.Double)">
            <summary>
            Converts to points dips.
            </summary>
            <param name="value">Picas.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.EmuToDip(System.Double)">
            <summary>
            Converts EMUs to dips.
            </summary>
            <param name="value">EMUs.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.CmToDip(System.Double)">
            <summary>
            Converts centimeters to dips.
            </summary>
            <param name="value">Centimeters.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.MmToDip(System.Double)">
            <summary>
            Converts millimeters to dips.
            </summary>
            <param name="value">Millimeters.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.InchToDip(System.Double)">
            <summary>
            Converts inches to dips.
            </summary>
            <param name="value">Inches.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDip(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.UnitToDip(System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts Units to dips.
            </summary>
            <param name="value">Units.</param>
            <param name="type">UnitType.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToEm(System.Double,System.Double)">
            <summary>
            Converts pixels to units of measurement.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixels.</param>
            <returns>Ems.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.EmToPixel(System.Double,System.Double)">
            <summary>
            Converts units of measurement  to pixels.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Ems.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToPercent(System.Double,System.Double)">
            <summary>
            Converts pixels to percents.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixels.</param>
            <returns>Percents.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PercentToPixel(System.Double,System.Double)">
            <summary>
            Converts percents to pixels.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Percents.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.UnitToPixel(System.Double,System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
             Converts Units to pixel.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">The value.</param>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToUnit(System.Double,System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts Pixels the unit.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixel.</param>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.IsRelativeUnitType(Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Determines whether unit type is relative.
            </summary>
            <param name="type">The unit type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.UnitType">
            <summary>
            Defines different unit types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Dip">
            <summary> Device independent pixel.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Point">
            <summary> Point.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Pica">
            <summary> Pica.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Inch">
            <summary> Inch.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Mm">
            <summary> Millimeter.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Cm">
            <summary> Centimeter.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Twip">
            <summary> Twip - twentieth of a point.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Emu">
            <summary> EMU - English Metric Unit.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Percent">
            <summary>Percentage.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Em">
            <summary>Em.</summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Primitives.Padding">
            <summary>
            Represents padding or margin information.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Primitives.Padding.Empty">
            <summary>
            An empty padding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Primitives.Padding"/> class and sets all paddings to a given value.
            </summary>
            <param name="all">The value in device independent pixels (1/96 inch).</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Primitives.Padding"/> class.
            </summary>
            <param name="left">The left padding in device independent pixels (1/96 inch).</param>
            <param name="top">The top padding in device independent pixels (1/96 inch).</param>
            <param name="right">The right padding in device independent pixels (1/96 inch).</param>
            <param name="bottom">The bottom padding in device independent pixels (1/96 inch).</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            Returns true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.op_Equality(Telerik.Windows.Documents.Primitives.Padding,Telerik.Windows.Documents.Primitives.Padding)">
            <summary>
            Determines whether the specified paddings are equal.
            </summary>
            <returns>True if the paddings are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.op_Inequality(Telerik.Windows.Documents.Primitives.Padding,Telerik.Windows.Documents.Primitives.Padding)">
            <summary>
            Determines whether the specified paddings are different.
            </summary>
            <returns>True if the paddings are different.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.ToString">
            <inheritdoc />
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Top">
            <summary>
            Gets the top padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The top padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Bottom">
            <summary>
            Gets the bottom padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The bottom padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Left">
            <summary>
            Gets the left padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The left padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Right">
            <summary>
            Gets the right padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The right padding.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Fonts.FontProperties">
            <summary>
            Represents fonts properties class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.#ctor(System.Windows.Media.FontFamily,System.Windows.FontStyle,System.Windows.FontWeight)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Fonts.FontProperties"/> class.
            </summary>
            <param name="fontFamily">The font family.</param>
            <param name="fontStyle">The font style.</param>
            <param name="fontWeight">The font weight.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.#ctor(System.Windows.Media.FontFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Fonts.FontProperties"/> class.
            </summary>
            <param name="fontFamily">The font family.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontFamilyName">
            <summary>
            Gets font family name.
            </summary>
            <value>The name of the font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontWeight">
            <summary>
            Gets the font weight.
            </summary>
            <value>The font weight.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontStyle">
            <summary>
            Gets the font style.
            </summary>
            <value>The font style.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontFamily">
            <summary>
            Gets the font family.
            </summary>
            <value>The font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.IsMonoSpaced">
            <summary>
            Gets if font is mono spaced.
            </summary>
            <value>The is mono spaced.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition">
            <summary>
            Represents sub string position enum. This position is used when RTL text is measured.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.None">
            <summary>
            Represents the default substring position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.Start">
            <summary>
            Represents position in start of string.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.Middle">
            <summary>
            Represents position in middle of string.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.End">
            <summary>
            Represents position in end of string.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.DiscreteCosineTransform.ForwardDCT(Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.Block)">
            <summary>
            Implements Fast FDCT.
            </summary>
            <param name="input">Input block.</param>
            <returns>Output block.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.AdobeMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineHuffmanTableMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineQuantizationTableMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineRestartIntervalMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.EndOfImageMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.APP0">
            <summary>
            JFIF application marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.APP14">
            <summary>
            Adobe application marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.COM">
            <summary>
            Comment.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DHT">
            <summary>
            Define huffman table marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DQT">
            <summary>
            Define quantization table marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DRI">
            <summary>
            Define restart interval.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.EOI">
            <summary>
            End of image.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.RST">
            <summary>
            Restart scan.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOF">
            <summary>
            Start of frame.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOI">
            <summary>
            Start of image.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOS">
            <summary>
            Start of scan.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.NotSupported">
            <summary>
            Not supported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.NotSupportedMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.RestartMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfFrameMarker.EncodingType">
            <summary>
            Gets encoding type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfFrameMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfImageMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfScanMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable">
            <summary>
            Base class for the JPEG information structures.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG information structure.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG information structure.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Length">
            <summary>
            Gets length of the table.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.AddComponent(Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegFrameComponent)">
            <summary>
            Add frame component (image color component).
            </summary>
            <param name="component">JPEG frame component to add.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG frame header.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG frame header.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.ImageComponents">
            <summary>
            Gets number of image components in frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see Nf parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Height">
            <summary>
            Gets number of lines (height). 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Length">
            <summary>
            Gets length of the frame header.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see Lf parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.SamplePrecision">
            <summary>
            Gets the precision in bits for the samples of the components in the frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see P parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Width">
            <summary>
            Gets number of the samples per line (width).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads Huffman table.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes Huffman table.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Length">
            <summary>
            Gets a length of the Huffman table.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.2 Huffman table-specification syntax (see Tc, Th, Li and Vi,j parameters in the Table B.5).</remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.#ctor">
            <summary>
            Initializes a new instance of the QuantizationTable class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.#ctor(System.Byte,System.Byte[])">
            <summary>
            Initializes a new instance of the QuantizationTable class.
            </summary>
            <param name="tableIndex">Table index</param>
            <param name="byteData">Table data.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads all quantization table parameters.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes all quantization table parameters.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Length">
            <summary>
            Gets a length of all quantization table parameters.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.1 Quantization table-specification syntax (see Pq, Tq and Qr parameters in the Table B.4).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Precision">
            <summary>
            Gets Quantization table element precision.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.1 Quantization table-specification syntax (see Pq parameter in the Table B.4).
            Specifies the precision of the Qk values. Value 0 indicates 8-bit Qk values; value 1 indicates 16-bit Qk values. 
            Pq shall be zero for 8 bit sample precision P (see B.2.2).
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.AddComponent(System.Int32,Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegScanComponent)">
            <summary>
            Add frame component (image color component).
            </summary>
            <param name="index">Index of the component.</param>
            <param name="component">JPEG frame component to add.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG scan header.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG scan header.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.BitPositionHigh">
            <summary>
            Gets Successive approximation bit position high.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ah parameter in the Table B.3).
            This parameter specifies the point transform used in the preceding scan (i.e. successive approximation bit position low 
            in the preceding scan) for the band of coefficients specified by Ss and Se. This parameter shall be set to zero for the 
            first scan of each band of coefficients. In the lossless mode of operations this parameter has no meaning. It shall be set to zero.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.BitPositionLow">
            <summary>
            Gets Successive approximation bit position low or point transform.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Al parameter in the Table B.3).
            In the DCT modes of operation this parameter specifies the point transform, i.e. bit position low, used before coding the band 
            of coefficients specified by Ss and Se. This parameter shall be set to zero for the sequential DCT processes. In the lossless 
            mode of operations, this parameter specifies the point transform, Pt.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Length">
            <summary>
            Gets a scan header length.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ls parameter in the Table B.3).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.SpectralSelectionEnd">
            <summary>
            Gets End of spectral selection.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Se parameter in the Table B.3).
            Specifies the last DCT coefficient in each block in zig-zag order which shall be coded in the scan. 
            This parameter shall be set to 63 for the sequential DCT processes. In the lossless mode of operations 
            this parameter has no meaning. It shall be set to zero.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.SpectralSelectionStart">
            <summary>
            Gets Start of spectral or predictor selection.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ss parameter in the Table B.3).
            In the DCT modes of operation, this parameter specifies the first DCT coefficient in each block in zig-zag order 
            which shall be coded in the scan. This parameter shall be set to zero for the sequential DCT processes. 
            In the lossless mode of operations this parameter is used to select the predictor.
            </remarks>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.ITextMeasurer">
            <summary>
            Represents base text measurer interface.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.ITextMeasurer.MeasureText(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties)">
            <summary>
            Measures the text.
            </summary>
            <param name="textProperties">The text properties.</param>
            <param name="fontProperties">The font properties.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer">
            <summary>
            Represents text measurer that can be used in multi-threaded applications.
            </summary>
            <summary>
            Represents text measurer that can be used in multi-threaded applications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer.MeasureText(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties)">
            <summary>
            Measures the text.
            </summary>
            <param name="textProperties"></param>
            <param name="fontProperties">The font properties.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer.MeasureTextWithWrapping(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties,System.Double)">
            <summary>
            Measures the text with wrapping.
            </summary>
            <param name="textProperties">The text properties.</param>
            <param name="fontProperties">The font properties.</param>
            <param name="wrappingWidth">Width of the wrapping.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.SystemFontsManager.WarmUp">
            <summary>
            This method will trigger the initial load of system fonts.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties">
            <summary>
            Represents text properties.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.#ctor(System.String,System.Double,Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties"/> class.
            </summary>
            <param name="text">The text.</param>
            <param name="size">The size.</param>
            <param name="subStringPosition">The sub string position.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.SubStringPosition">
            <summary>
            Gets the sub string position.
            </summary>
            <value>The sub string position.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.Size">
            <summary>
            Gets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.Text">
            <summary>
            Gets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo">
            <summary>
            Represents text measurement info class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.Empty">
            <summary>
            Gets empty text measurement info.
            </summary>
            <value>Empty text measurement info.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.Size">
            <summary>
            Gets or sets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.BaselineOffset">
            <summary>
            Gets or sets the baseline offset.
            </summary>
            <value>The baseline offset.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalMinimumAndMaximum(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Finds the minimum and maximum of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coeficient.</param>
            <param name="b">The b coeficient.</param>
            <param name="c">The c coeficient.</param>
            <param name="d">The free coeficient.</param>
            <returns>The bounding interval.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalMinimumAndMaximum(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double)">
            <summary>
            Finds the minimum and maximum of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coeficient.</param>
            <param name="b">The b coeficient.</param>
            <param name="c">The free coeficient.</param>
            <returns>The bounding interval.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalExtrema(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double)">
            <summary>
            Finds the local extrema of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coeficient.</param>
            <param name="b">The b coeficient.</param>
            <param name="c">The free coeficient.</param>
            <returns>The local extrema.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalExtrema(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Finds the local extrema of a cubic function a*x*x*x + b*x*x + c*x + d.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coeficient.</param>
            <param name="b">The b coeficient.</param>
            <param name="c">The c coeficient.</param>
            <param name="d">The free coeficient.</param>
            <returns>The local extrema.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.TrySolveQuadratic(System.Double,System.Double,System.Double,System.Double[]@)">
            <summary>
            Solves the quadratic equation a*x*x + b*x + c = 0.
            </summary>
            <param name="a">The coeficient before x*x.</param>
            <param name="b">The coeficient before x.</param>
            <param name="c">The free coeficient.</param>
            <param name="x">The x.</param>
            <returns>True if finite number of finite real solutions exist. Else returns false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.SolveQuadratic(System.Double,System.Double,System.Double)">
            <summary>
            Solves the quadratic equation a*x*x + b*x + c = 0.
            </summary>
            <param name="a">The coeficient before x*x.</param>
            <param name="b">The coeficient before x.</param>
            <param name="c">The free coeficient.</param>
            <returns>Returns the real solutions of the equation. Returns PositiveInfinity if every real number is solution.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.TrySolveLinear(System.Double,System.Double,System.Double@)">
            <summary>
            Solves the equation a*x + b = 0
            </summary>
            <param name="a">The coeficient before x.</param>
            <param name="b">The free coeficient.</param>
            <param name="x">The x.</param>
            <returns>True if finite number of finite real solution exists. Else returns false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.SolveLinear(System.Double,System.Double)">
            <summary>
            Solves the equation a*x + b = 0
            </summary>
            <param name="a">The coeficient before x.</param>
            <param name="b">The free coeficient.</param>
            <returns>The appropriate value of x. Returns NaN if no solution is available. Returns PositiveInfinity when every x is solution.</returns>
        </member>
    </members>
</doc>
