﻿using System.Collections.Generic;

namespace AnkaraSigorta.Model
{
    public class Fault
    {
        public string Code { get; set; }
        public string String { get; set; }
        public string Message { get; set; }
        public List<Error> Errors { get; set; } = new List<Error>();
        public bool IsFailed { get { return Errors != null && Errors.Count > 0 || !string.IsNullOrWhiteSpace(Message) || !string.IsNullOrWhiteSpace(String); } }

        public class Error
        {
            public string Code { get; set; }
            public string Message { get; set; }
        }
    }
}
