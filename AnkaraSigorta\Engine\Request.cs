﻿using System;
using System.Xml;

namespace AnkaraSigorta.Engine
{
    public class Request
    {
        public static XmlDocument CreateXmlDocument(string body)
        {
            return CreateXmlDocument(body, "");
        }

        public static XmlDocument CreateXmlDocument(string body, string contractNamespace)
        {
            string contractNamespaceAttr = string.IsNullOrWhiteSpace(contractNamespace) ? "" : string.Format(" xmlns:ank=\"{0}\"", contractNamespace);

            string soapContent = string.Format(@"<soapenv:Envelope xmlns:soapenv=""http://schemas.xmlsoap.org/soap/envelope/"" xmlns:ws=""http://ws.ankarasigorta.com.tr""{0}><soapenv:Header>" +
                GetHeader() + @"</soapenv:Header><soapenv:Body>" + body + @"</soapenv:Body></soapenv:Envelope>",
                contractNamespaceAttr);          

            XmlDocument soapXml = new XmlDocument();
            soapXml.LoadXml(soapContent);
            return soapXml;
        }

        private static string GetHeader()
        {
            return string.Format(@"<wsse:Security xmlns:wsse=""http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd"">" +
                @"<wsse:UsernameToken><wsse:Username>{0}</wsse:Username><wsse:Password>{1}</wsse:Password></wsse:UsernameToken></wsse:Security>",
               Undone.Config.Manager.GetKeyValue("AnkaraSigorta.Username"),
               Undone.Config.Manager.GetKeyValue("AnkaraSigorta.Password"));
        }

        public static string SendRequest(string postUrl, string action, XmlDocument soapXml)
        {
            return SendRequest(postUrl, action, soapXml, null);
        }

        public static string SendRequest(string postUrl, string action, XmlDocument soapXml, Model.Log log)
        {
            string result = "";
            bool isLoggerOn = log != null && Undone.Config.Manager.GetKeyValue("AnkaraSigorta.IsLoggerOn").ToLower() == "true";

            try
            {
                if (isLoggerOn)
                {
                    log.Url = postUrl;
                    log.Request = soapXml.OuterXml;
                }

                System.Net.HttpWebRequest request = (System.Net.HttpWebRequest)System.Net.WebRequest.Create(postUrl);

                request.Headers.Add("SOAPAction", action);
                request.ContentType = "text/xml;charset=\"utf-8\"";
                request.Accept = "text/xml";
                request.Method = "POST";

                using (System.IO.Stream stream = request.GetRequestStream())
                {
                    soapXml.Save(stream);
                }

                using (System.Net.WebResponse response = request.GetResponse())
                {
                    using (System.IO.StreamReader reader = new System.IO.StreamReader(response.GetResponseStream()))
                    {
                        result = reader.ReadToEnd();

                        if (isLoggerOn)
                            log.Response = result;
                    }
                }
            }
            catch (System.Net.WebException ex)
            {
                if (ex.Response != null)
                {
                    using (System.Net.HttpWebResponse errorResponse = (System.Net.HttpWebResponse)ex.Response)
                    {
                        using (System.IO.StreamReader reader = new System.IO.StreamReader(errorResponse.GetResponseStream()))
                        {
                            result = reader.ReadToEnd();

                            if (isLoggerOn)
                                log.Error = result;
                        }
                    }
                }
                else
                {
                    if (isLoggerOn)
                        log.Error = ex.Message + Environment.NewLine + ex.StackTrace;

                    result = string.Format(@"<s:Envelope xmlns:s=""http://schemas.xmlsoap.org/soap/envelope/""><s:Body><s:Fault><faultcode>SYSERR</faultcode>" +
                                @"<faultstring xml:lang=""tr-TR"">System Error</faultstring>" +
                                @"<detail><AnkaraFault xmlns=""http://ws.ankarasigorta.com.tr"" xmlns:i=""http://www.w3.org/2001/XMLSchema-instance""><Errors xmlns:a=""http://schemas.datacontract.org/2004/07/AnkaraSigorta.Core.WS"">" +
                                @"<a:ErrorInfo><a:Code>0</a:Code><a:Message>{0}</a:Message></a:ErrorInfo></Errors><Message>{1}</Message></AnkaraFault></detail></s:Fault></s:Body></s:Envelope>",
                                    ex.Message,
                                    ex.Message);
                }
            }

            if (isLoggerOn)
                Logger.Log(log);

            return result;
        }

        public static string GetOuterXml(XmlDocument xmlDocument)
        {
            return xmlDocument.OuterXml;
        }
    }
}