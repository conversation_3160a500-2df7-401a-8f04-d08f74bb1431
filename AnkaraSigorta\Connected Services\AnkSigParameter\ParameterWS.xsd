<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://ws.ankarasigorta.com.tr" elementFormDefault="qualified" targetNamespace="http://ws.ankarasigorta.com.tr" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/ParameterWS.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" />
  <xs:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/ParameterWS.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/AnkaraSigorta.Core.WS" />
  <xs:element name="GetCities">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetCitiesResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="GetCitiesResult" nillable="true" type="q1:ArrayOfCity" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:complexType name="AnkaraFault">
    <xs:sequence>
      <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/AnkaraSigorta.Core.WS" minOccurs="0" name="Errors" nillable="true" type="q2:ArrayOfErrorInfo" />
      <xs:element minOccurs="0" name="Message" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="AnkaraFault" nillable="true" type="tns:AnkaraFault" />
  <xs:element name="GetTowns">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="city" nillable="true" type="q3:City" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetTownsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="GetTownsResult" nillable="true" type="q4:ArrayOfTown" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetMunicipals">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="city" nillable="true" type="q5:City" />
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="town" nillable="true" type="q6:Town" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetMunicipalsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q7="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="GetMunicipalsResult" nillable="true" type="q7:ArrayOfMunicipal" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNationalities">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetNationalitiesResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q8="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" minOccurs="0" name="GetNationalitiesResult" nillable="true" type="q8:ArrayOfNationality" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>