<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:i0="http://tempuri.org/" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://ws.ankarasigorta.com.tr" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="PolicyWS" targetNamespace="http://ws.ankarasigorta.com.tr" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:import namespace="http://tempuri.org/" location="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?wsdl=wsdl0" />
  <wsdl:types>
    <xsd:schema targetNamespace="http://ws.ankarasigorta.com.tr/Imports">
      <xsd:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?xsd=xsd0" namespace="http://ws.ankarasigorta.com.tr" />
      <xsd:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/AnkaraSigorta.ExtApps.WS.Models" />
      <xsd:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/AnkaraSigorta.Core.WS" />
      <xsd:import schemaLocation="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc?xsd=xsd4" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IPolicyWS_GetPrintout_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPrintout" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPrintout_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPrintoutResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPrintout_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCreditCardInstallments_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCreditCardInstallments" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCreditCardInstallments_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCreditCardInstallmentsResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCreditCardInstallments_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCreditCard_InputMessage">
    <wsdl:part name="parameters" element="tns:ConfirmCreditCard" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCreditCard_OutputMessage">
    <wsdl:part name="parameters" element="tns:ConfirmCreditCardResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCreditCard_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCashInstallments_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCashInstallments" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCashInstallments_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCashInstallmentsResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCashInstallments_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCash_InputMessage">
    <wsdl:part name="parameters" element="tns:ConfirmCash" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCash_OutputMessage">
    <wsdl:part name="parameters" element="tns:ConfirmCashResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_ConfirmCash_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCryptoKey_InputMessage">
    <wsdl:part name="parameters" element="tns:GetCryptoKey" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCryptoKey_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetCryptoKeyResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetCryptoKey_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_Decrypt_InputMessage">
    <wsdl:part name="parameters" element="tns:Decrypt" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_Decrypt_OutputMessage">
    <wsdl:part name="parameters" element="tns:DecryptResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_Decrypt_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyDetail_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPolicyDetail" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyDetail_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPolicyDetailResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyDetail_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyKeyList_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPolicyKeyList" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyKeyList_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPolicyKeyListResponse" />
  </wsdl:message>
  <wsdl:message name="IPolicyWS_GetPolicyKeyList_AnkaraFault_FaultMessage">
    <wsdl:part name="detail" element="tns:AnkaraFault" />
  </wsdl:message>
  <wsdl:portType name="IPolicyWS">
    <wsdl:operation name="GetPrintout">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPrintout" message="tns:IPolicyWS_GetPrintout_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPrintoutResponse" message="tns:IPolicyWS_GetPrintout_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetPrintout_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCreditCardInstallments">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCreditCardInstallments" message="tns:IPolicyWS_GetCreditCardInstallments_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCreditCardInstallmentsResponse" message="tns:IPolicyWS_GetCreditCardInstallments_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetCreditCardInstallments_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="ConfirmCreditCard">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/ConfirmCreditCard" message="tns:IPolicyWS_ConfirmCreditCard_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/ConfirmCreditCardResponse" message="tns:IPolicyWS_ConfirmCreditCard_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_ConfirmCreditCard_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCashInstallments">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCashInstallments" message="tns:IPolicyWS_GetCashInstallments_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCashInstallmentsResponse" message="tns:IPolicyWS_GetCashInstallments_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetCashInstallments_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="ConfirmCash">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/ConfirmCash" message="tns:IPolicyWS_ConfirmCash_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/ConfirmCashResponse" message="tns:IPolicyWS_ConfirmCash_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_ConfirmCash_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetCryptoKey">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCryptoKey" message="tns:IPolicyWS_GetCryptoKey_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetCryptoKeyResponse" message="tns:IPolicyWS_GetCryptoKey_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetCryptoKey_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="Decrypt">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/Decrypt" message="tns:IPolicyWS_Decrypt_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/DecryptResponse" message="tns:IPolicyWS_Decrypt_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_Decrypt_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPolicyDetail">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPolicyDetail" message="tns:IPolicyWS_GetPolicyDetail_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPolicyDetailResponse" message="tns:IPolicyWS_GetPolicyDetail_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetPolicyDetail_AnkaraFault_FaultMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetPolicyKeyList">
      <wsdl:input wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPolicyKeyList" message="tns:IPolicyWS_GetPolicyKeyList_InputMessage" />
      <wsdl:output wsaw:Action="http://ws.ankarasigorta.com.tr/IPolicyWS/GetPolicyKeyListResponse" message="tns:IPolicyWS_GetPolicyKeyList_OutputMessage" />
      <wsdl:fault wsaw:Action="" name="AnkaraFault" message="tns:IPolicyWS_GetPolicyKeyList_AnkaraFault_FaultMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:service name="PolicyWS">
    <wsdl:port name="BasicHttpBinding_IPolicyWS" binding="i0:BasicHttpBinding_IPolicyWS">
      <soap:address location="http://tst-ws.ankarasigorta.com.tr/PolicyWS.svc" />
    </wsdl:port>
    <wsdl:port name="BasicHttpsBinding_IPolicyWS" binding="i0:BasicHttpsBinding_IPolicyWS">
      <soap:address location="https://tst-ws.ankarasigorta.com.tr/PolicyWS.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>