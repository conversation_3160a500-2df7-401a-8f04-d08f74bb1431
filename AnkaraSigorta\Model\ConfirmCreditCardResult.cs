﻿using AnkaraSigorta.Attributes;
using System;

namespace AnkaraSigorta.Model
{
    public class ConfirmCreditCardResult
    {
        [SoapName("a:Commission")]
        public double Commission { get; set; }

        [SoapName("a:CommissionInCurrency")]
        public double CommissionInCurrency { get; set; }

        [SoapName("a:Currency")]
        public string Currency { get; set; }

        [SoapName("a:Premium")]
        public double Premium { get; set; }

        [SoapName("a:PremiumInCurrency")]
        public double PremiumInCurrency { get; set; }

        [SoapName("a:EndorsNumber")]
        public Int64 EndorsNumber { get; set; }

        [SoapName("a:PolicyNumber")]
        public Int64 PolicyNumber { get; set; }

        [SoapName("a:ProductNumber")]
        public Int64 ProductNumber { get; set; }

        [SoapName("a:RenewalNumber")]
        public Int64 RenewalNumber { get; set; }

        public Fault Fault { get; set; }
    }
}
